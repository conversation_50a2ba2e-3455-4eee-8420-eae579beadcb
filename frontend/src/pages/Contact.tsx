import React, { useEffect, useRef } from 'react';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/hooks/use-toast';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Send,
  ArrowRight,
} from 'lucide-react';

const formSchema = z.object({
  name: z.string().min(2, { message: 'Name is required' }),
  email: z.string().email({ message: 'Invalid email address' }),
  phone: z.string().min(10, { message: 'Valid phone number is required' }),
  subject: z.string().min(5, { message: 'Subject is required' }),
  message: z
    .string()
    .min(10, { message: 'Message must be at least 10 characters' }),
});

const Contact = () => {
  const { toast } = useToast();
  const formRef = useRef<HTMLDivElement>(null);
  const infoRef = useRef<HTMLDivElement>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
    },
  });

  // Animation for elements when they come into view
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-visible');
          }
        });
      },
      { threshold: 0.1 }
    );

    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach((el) => observer.observe(el));

    return () => {
      animatedElements.forEach((el) => observer.unobserve(el));
    };
  }, []);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      // This would normally be an API call
      console.log('Form values:', values);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: 'Message Sent',
        description:
          'Thank you for contacting us. We will get back to you soon.',
      });

      form.reset();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send message. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <RRUHeader />
      <Header />

      <main className="flex-grow bg-white">
        {/* Hero Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gyaan-navy">
                Contact Us
              </h1>
              <div className="w-24 h-1 bg-gyaan-gold mx-auto mb-6"></div>
              <p className="text-xl text-gray-600">
                We're here to help with any questions about GyaanRaksha Samyog's
                programs and initiatives.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Form and Information */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Contact Form */}
                <div ref={formRef}>
                  <h2 className="text-2xl md:text-3xl font-bold mb-8 text-gyaan-navy">
                    Send Us a Message
                  </h2>

                  <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-200 transition-all duration-300 hover:scale-105 hover:shadow-xl cursor-pointer">
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6"
                      >
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gyaan-navy font-medium">
                                Full Name
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Enter your full name"
                                  className="border-gray-300 focus-visible:ring-gyaan-navy"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-gyaan-navy font-medium">
                                  Email
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Enter your email"
                                    className="border-gray-300 focus-visible:ring-gyaan-navy"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-gyaan-navy font-medium">
                                  Phone Number
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Enter your phone number"
                                    className="border-gray-300 focus-visible:ring-gyaan-navy"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name="subject"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gyaan-navy font-medium">
                                Subject
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Enter subject"
                                  className="border-gray-300 focus-visible:ring-gyaan-navy"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="message"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gyaan-navy font-medium">
                                Message
                              </FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Enter your message"
                                  className="min-h-[150px] border-gray-300 focus-visible:ring-gyaan-navy"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 text-white"
                        >
                          <Send className="h-4 w-4 mr-2" />
                          Send Message
                        </Button>
                      </form>
                    </Form>
                  </div>
                </div>

                {/* Contact Information */}
                <div ref={infoRef}>
                  <h2 className="text-2xl md:text-3xl font-bold mb-8 text-gyaan-navy">
                    Contact Information
                  </h2>

                  <div className="bg-gray-50 p-8 rounded-lg shadow-lg border border-gray-200 transition-all duration-300 hover:scale-105 hover:shadow-xl cursor-pointer">
                    <div className="space-y-6">
                      <div className="flex items-start">
                        <div className="bg-gyaan-navy p-3 rounded-full mr-4">
                          <MapPin className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg text-gyaan-navy mb-2">
                            Address
                          </h3>
                          <p className="text-gray-600 leading-relaxed">
                            Rashtriya Raksha University
                            <br />
                            Lavad, Gandhinagar
                            <br />
                            Gujarat, India - 382305
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <div className="bg-gyaan-navy p-3 rounded-full mr-4">
                          <Phone className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg text-gyaan-navy mb-2">
                            Phone
                          </h3>
                          <p className="text-gray-600">
                            <a
                              href="tel:+************"
                              className="hover:text-gyaan-navy transition-colors"
                            >
                              +91 12345 67890
                            </a>
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <div className="bg-gyaan-navy p-3 rounded-full mr-4">
                          <Mail className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg text-gyaan-navy mb-2">
                            Email
                          </h3>
                          <p className="text-gray-600">
                            <a
                              href="mailto:<EMAIL>"
                              className="hover:text-gyaan-navy transition-colors"
                            >
                              <EMAIL>
                            </a>
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <div className="bg-gyaan-navy p-3 rounded-full mr-4">
                          <Clock className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg text-gyaan-navy mb-2">
                            Working Hours
                          </h3>
                          <p className="text-gray-600 leading-relaxed">
                            Monday - Friday: 9:00 AM - 5:00 PM
                            <br />
                            Saturday: 9:00 AM - 1:00 PM
                            <br />
                            Sunday: Closed
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-8 pt-6 border-t border-gray-300">
                      <h3 className="font-semibold text-lg text-gyaan-navy mb-4">
                        Connect With Us
                      </h3>
                      <div className="flex space-x-4">
                        <a
                          href="https://www.facebook.com/RakshaUni/"
                          className="bg-gyaan-navy p-3 rounded-full hover:bg-gyaan-navy/80 transition-colors"
                        >
                          <Facebook className="h-5 w-5 text-white" />
                        </a>
                        <a
                          href="https://x.com/RakshaUni"
                          className="bg-gyaan-navy p-3 rounded-full hover:bg-gyaan-navy/80 transition-colors"
                        >
                          <Twitter className="h-5 w-5 text-white" />
                        </a>
                        <a
                          href="https://www.instagram.com/rakshauni/?hl=en"
                          className="bg-gyaan-navy p-3 rounded-full hover:bg-gyaan-navy/80 transition-colors"
                        >
                          <Instagram className="h-5 w-5 text-white" />
                        </a>
                        <a
                          href="https://www.linkedin.com/school/rakshauni/posts/"
                          className="bg-gyaan-navy p-3 rounded-full hover:bg-gyaan-navy/80 transition-colors"
                        >
                          <Linkedin className="h-5 w-5 text-white" />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Contact;
