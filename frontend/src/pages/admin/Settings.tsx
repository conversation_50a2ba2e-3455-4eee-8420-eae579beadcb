import React, { useState } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { usePermissions } from '@/hooks/use-permissions';
import { Permission } from '@/utils/rbac';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Settings,
  Mail,
  Bell,
  Globe,
  Shield,
  Palette,
  FileText,
  Smartphone,
  Save,
  Loader2,
  AlertCircle,
  CheckCircle,
  RefreshCw,
} from 'lucide-react';

// Interface for system settings
interface SystemSettings {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  supportPhone: string;
  maintenanceMode: boolean;
  allowRegistration: boolean;
  defaultLanguage: string;
  dateFormat: string;
  timeFormat: string;
  timezone: string;
  logoUrl: string;
  faviconUrl: string;
  primaryColor: string;
  secondaryColor: string;
}

// Interface for email settings
interface EmailSettings {
  smtpServer: string;
  smtpPort: string;
  smtpUsername: string;
  smtpPassword: string;
  senderEmail: string;
  senderName: string;
  enableSsl: boolean;
  emailFooter: string;
}

// Interface for notification settings
interface NotificationSettings {
  enableEmailNotifications: boolean;
  enableSmsNotifications: boolean;
  enablePushNotifications: boolean;
  adminEmailNotifications: boolean;
  userRegistrationNotifications: boolean;
  paymentNotifications: boolean;
  courseEnrollmentNotifications: boolean;
  systemAlertNotifications: boolean;
}

// Interface for security settings
interface SecuritySettings {
  passwordMinLength: string;
  passwordRequireUppercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSymbols: boolean;
  accountLockoutAttempts: string;
  accountLockoutDuration: string;
  sessionTimeout: string;
  enableTwoFactorAuth: boolean;
  allowSocialLogin: boolean;
}

const AdminSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { can } = usePermissions();

  // State for different settings sections
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    siteName: 'GyaanRaksha Samyog Portal',
    siteDescription: 'Police Training and Education Portal',
    contactEmail: '<EMAIL>',
    supportPhone: '+91 **********',
    maintenanceMode: false,
    allowRegistration: true,
    defaultLanguage: 'en',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24',
    timezone: 'Asia/Kolkata',
    logoUrl: '/src/assets/images/RRULogo.png',
    faviconUrl: '/src/assets/images/RRULogo.png',
    primaryColor: '#1e3a8a',
    secondaryColor: '#0ea5e9',
  });

  const [emailSettings, setEmailSettings] = useState<EmailSettings>({
    smtpServer: 'smtp.gyaanraksha.gov.in',
    smtpPort: '587',
    smtpUsername: '<EMAIL>',
    smtpPassword: '********',
    senderEmail: '<EMAIL>',
    senderName: 'GyaanRaksha Portal',
    enableSsl: true,
    emailFooter: '© 2023 GyaanRaksha Samyog Portal. All rights reserved.',
  });

  const [notificationSettings, setNotificationSettings] =
    useState<NotificationSettings>({
      enableEmailNotifications: true,
      enableSmsNotifications: true,
      enablePushNotifications: false,
      adminEmailNotifications: true,
      userRegistrationNotifications: true,
      paymentNotifications: true,
      courseEnrollmentNotifications: true,
      systemAlertNotifications: true,
    });

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    passwordMinLength: '8',
    passwordRequireUppercase: true,
    passwordRequireNumbers: true,
    passwordRequireSymbols: false,
    accountLockoutAttempts: '5',
    accountLockoutDuration: '30',
    sessionTimeout: '60',
    enableTwoFactorAuth: false,
    allowSocialLogin: false,
  });

  // Handle system settings change
  const handleSystemSettingsChange = (
    field: keyof SystemSettings,
    value: string | boolean
  ) => {
    setSystemSettings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle email settings change
  const handleEmailSettingsChange = (
    field: keyof EmailSettings,
    value: string | boolean
  ) => {
    setEmailSettings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle notification settings change
  const handleNotificationSettingsChange = (
    field: keyof NotificationSettings,
    value: boolean
  ) => {
    setNotificationSettings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle security settings change
  const handleSecuritySettingsChange = (
    field: keyof SecuritySettings,
    value: string | boolean
  ) => {
    setSecuritySettings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    if (!can(Permission.MANAGE_SYSTEM_SETTINGS)) {
      toast({
        title: 'Permission Denied',
        description: 'You do not have permission to update system settings.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSaving(true);

      // In a real app, we would send data to the API
      // For now, we'll just simulate a delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      toast({
        title: 'Settings Saved',
        description: 'System settings have been updated successfully.',
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Test email configuration
  const handleTestEmailConfig = async () => {
    try {
      setIsLoading(true);

      // In a real app, we would send a test email
      // For now, we'll just simulate a delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast({
        title: 'Test Email Sent',
        description: 'A test email has been sent successfully.',
      });
    } catch (error) {
      console.error('Error sending test email:', error);
      toast({
        title: 'Error',
        description:
          'Failed to send test email. Please check your configuration.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gyaan-navy">
              System Settings
            </h1>
            <p className="text-gray-600 mt-1">
              Configure system-wide settings and preferences
            </p>
          </div>

          <Button
            onClick={handleSaveSettings}
            disabled={isSaving || !can(Permission.MANAGE_SYSTEM_SETTINGS)}
            className="bg-gyaan-navy hover:bg-gyaan-navy/90"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </>
            )}
          </Button>
        </div>

        <Tabs
          defaultValue="general"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general" className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              General
            </TabsTrigger>
            <TabsTrigger value="email" className="flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              Email
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center">
              <Shield className="h-4 w-4 mr-2" />
              Security
            </TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Configure basic system settings and information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="siteName">Site Name</Label>
                      <Input
                        id="siteName"
                        value={systemSettings.siteName}
                        onChange={(e) =>
                          handleSystemSettingsChange('siteName', e.target.value)
                        }
                        placeholder="Enter site name"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="siteDescription">Site Description</Label>
                      <Textarea
                        id="siteDescription"
                        value={systemSettings.siteDescription}
                        onChange={(e) =>
                          handleSystemSettingsChange(
                            'siteDescription',
                            e.target.value
                          )
                        }
                        placeholder="Enter site description"
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="contactEmail">Contact Email</Label>
                      <Input
                        id="contactEmail"
                        type="email"
                        value={systemSettings.contactEmail}
                        onChange={(e) =>
                          handleSystemSettingsChange(
                            'contactEmail',
                            e.target.value
                          )
                        }
                        placeholder="Enter contact email"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="supportPhone">Support Phone</Label>
                      <Input
                        id="supportPhone"
                        value={systemSettings.supportPhone}
                        onChange={(e) =>
                          handleSystemSettingsChange(
                            'supportPhone',
                            e.target.value
                          )
                        }
                        placeholder="Enter support phone number"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="logoUrl">Logo URL</Label>
                      <Input
                        id="logoUrl"
                        value={systemSettings.logoUrl}
                        onChange={(e) =>
                          handleSystemSettingsChange('logoUrl', e.target.value)
                        }
                        placeholder="Enter logo URL"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="faviconUrl">Favicon URL</Label>
                      <Input
                        id="faviconUrl"
                        value={systemSettings.faviconUrl}
                        onChange={(e) =>
                          handleSystemSettingsChange(
                            'faviconUrl',
                            e.target.value
                          )
                        }
                        placeholder="Enter favicon URL"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="primaryColor">Primary Color</Label>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-6 h-6 rounded-full border"
                            style={{
                              backgroundColor: systemSettings.primaryColor,
                            }}
                          />
                          <Input
                            id="primaryColor"
                            value={systemSettings.primaryColor}
                            onChange={(e) =>
                              handleSystemSettingsChange(
                                'primaryColor',
                                e.target.value
                              )
                            }
                            placeholder="#000000"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="secondaryColor">Secondary Color</Label>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-6 h-6 rounded-full border"
                            style={{
                              backgroundColor: systemSettings.secondaryColor,
                            }}
                          />
                          <Input
                            id="secondaryColor"
                            value={systemSettings.secondaryColor}
                            onChange={(e) =>
                              handleSystemSettingsChange(
                                'secondaryColor',
                                e.target.value
                              )
                            }
                            placeholder="#000000"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="maintenanceMode">
                          Maintenance Mode
                        </Label>
                        <Switch
                          id="maintenanceMode"
                          checked={systemSettings.maintenanceMode}
                          onCheckedChange={(checked) =>
                            handleSystemSettingsChange(
                              'maintenanceMode',
                              checked
                            )
                          }
                        />
                      </div>
                      <p className="text-sm text-gray-500">
                        When enabled, the site will display a maintenance
                        message to all users.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="allowRegistration">
                          Allow Registration
                        </Label>
                        <Switch
                          id="allowRegistration"
                          checked={systemSettings.allowRegistration}
                          onCheckedChange={(checked) =>
                            handleSystemSettingsChange(
                              'allowRegistration',
                              checked
                            )
                          }
                        />
                      </div>
                      <p className="text-sm text-gray-500">
                        When enabled, new users can register on the portal.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium mb-4">
                    Localization Settings
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="defaultLanguage">Default Language</Label>
                      <Select
                        value={systemSettings.defaultLanguage}
                        onValueChange={(value) =>
                          handleSystemSettingsChange('defaultLanguage', value)
                        }
                      >
                        <SelectTrigger id="defaultLanguage">
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="hi">Hindi</SelectItem>
                          <SelectItem value="mr">Marathi</SelectItem>
                          <SelectItem value="gu">Gujarati</SelectItem>
                          <SelectItem value="pa">Punjabi</SelectItem>
                          <SelectItem value="bn">Bengali</SelectItem>
                          <SelectItem value="ta">Tamil</SelectItem>
                          <SelectItem value="te">Telugu</SelectItem>
                          <SelectItem value="kn">Kannada</SelectItem>
                          <SelectItem value="ml">Malayalam</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="dateFormat">Date Format</Label>
                      <Select
                        value={systemSettings.dateFormat}
                        onValueChange={(value) =>
                          handleSystemSettingsChange('dateFormat', value)
                        }
                      >
                        <SelectTrigger id="dateFormat">
                          <SelectValue placeholder="Select date format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                          <SelectItem value="DD-MMM-YYYY">
                            DD-MMM-YYYY
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="timeFormat">Time Format</Label>
                      <Select
                        value={systemSettings.timeFormat}
                        onValueChange={(value) =>
                          handleSystemSettingsChange('timeFormat', value)
                        }
                      >
                        <SelectTrigger id="timeFormat">
                          <SelectValue placeholder="Select time format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="12">12-hour (AM/PM)</SelectItem>
                          <SelectItem value="24">24-hour</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2 md:col-span-3">
                      <Label htmlFor="timezone">Timezone</Label>
                      <Select
                        value={systemSettings.timezone}
                        onValueChange={(value) =>
                          handleSystemSettingsChange('timezone', value)
                        }
                      >
                        <SelectTrigger id="timezone">
                          <SelectValue placeholder="Select timezone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Asia/Kolkata">
                            Asia/Kolkata (GMT+5:30)
                          </SelectItem>
                          <SelectItem value="Asia/Karachi">
                            Asia/Karachi (GMT+5:00)
                          </SelectItem>
                          <SelectItem value="Asia/Dhaka">
                            Asia/Dhaka (GMT+6:00)
                          </SelectItem>
                          <SelectItem value="Asia/Kathmandu">
                            Asia/Kathmandu (GMT+5:45)
                          </SelectItem>
                          <SelectItem value="Asia/Colombo">
                            Asia/Colombo (GMT+5:30)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  onClick={handleSaveSettings}
                  disabled={isSaving || !can(Permission.MANAGE_SYSTEM_SETTINGS)}
                  className="bg-gyaan-navy hover:bg-gyaan-navy/90"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="email" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Email Settings</CardTitle>
                <CardDescription>
                  Configure email server settings and templates
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="smtpServer">SMTP Server</Label>
                      <Input
                        id="smtpServer"
                        value={emailSettings.smtpServer}
                        onChange={(e) =>
                          handleEmailSettingsChange(
                            'smtpServer',
                            e.target.value
                          )
                        }
                        placeholder="e.g. smtp.example.com"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtpPort">SMTP Port</Label>
                      <Input
                        id="smtpPort"
                        value={emailSettings.smtpPort}
                        onChange={(e) =>
                          handleEmailSettingsChange('smtpPort', e.target.value)
                        }
                        placeholder="e.g. 587"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtpUsername">SMTP Username</Label>
                      <Input
                        id="smtpUsername"
                        value={emailSettings.smtpUsername}
                        onChange={(e) =>
                          handleEmailSettingsChange(
                            'smtpUsername',
                            e.target.value
                          )
                        }
                        placeholder="Enter SMTP username"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="smtpPassword">SMTP Password</Label>
                      <Input
                        id="smtpPassword"
                        type="password"
                        value={emailSettings.smtpPassword}
                        onChange={(e) =>
                          handleEmailSettingsChange(
                            'smtpPassword',
                            e.target.value
                          )
                        }
                        placeholder="Enter SMTP password"
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="enableSsl">Enable SSL/TLS</Label>
                        <Switch
                          id="enableSsl"
                          checked={emailSettings.enableSsl}
                          onCheckedChange={(checked) =>
                            handleEmailSettingsChange('enableSsl', checked)
                          }
                        />
                      </div>
                      <p className="text-sm text-gray-500">
                        Enable secure connection for email sending
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="senderEmail">Sender Email</Label>
                      <Input
                        id="senderEmail"
                        type="email"
                        value={emailSettings.senderEmail}
                        onChange={(e) =>
                          handleEmailSettingsChange(
                            'senderEmail',
                            e.target.value
                          )
                        }
                        placeholder="e.g. <EMAIL>"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="senderName">Sender Name</Label>
                      <Input
                        id="senderName"
                        value={emailSettings.senderName}
                        onChange={(e) =>
                          handleEmailSettingsChange(
                            'senderName',
                            e.target.value
                          )
                        }
                        placeholder="e.g. GyaanRaksha Portal"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="emailFooter">Email Footer</Label>
                      <Textarea
                        id="emailFooter"
                        value={emailSettings.emailFooter}
                        onChange={(e) =>
                          handleEmailSettingsChange(
                            'emailFooter',
                            e.target.value
                          )
                        }
                        placeholder="Enter email footer text"
                        rows={3}
                      />
                    </div>

                    <div className="pt-4">
                      <Button
                        variant="outline"
                        onClick={handleTestEmailConfig}
                        disabled={isLoading}
                        className="w-full"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Sending Test Email...
                          </>
                        ) : (
                          <>
                            <Mail className="h-4 w-4 mr-2" />
                            Send Test Email
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-6">
                  <h3 className="text-lg font-medium mb-4">Email Templates</h3>
                  <div className="bg-gray-50 p-6 rounded-md text-center">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium mb-2">
                      Email Template Editor
                    </h4>
                    <p className="text-gray-500 mb-4">
                      The email template editor allows you to customize the
                      appearance and content of system emails.
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        toast({
                          title: 'Feature Coming Soon',
                          description:
                            'The email template editor will be available in a future update.',
                        });
                      }}
                    >
                      Open Template Editor
                    </Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  onClick={handleSaveSettings}
                  disabled={isSaving || !can(Permission.MANAGE_SYSTEM_SETTINGS)}
                  className="bg-gyaan-navy hover:bg-gyaan-navy/90"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Configure system notifications and alerts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">
                      Notification Channels
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="enableEmailNotifications"
                            className="text-base"
                          >
                            Email Notifications
                          </Label>
                          <p className="text-sm text-gray-500">
                            Send notifications via email
                          </p>
                        </div>
                        <Switch
                          id="enableEmailNotifications"
                          checked={
                            notificationSettings.enableEmailNotifications
                          }
                          onCheckedChange={(checked) =>
                            handleNotificationSettingsChange(
                              'enableEmailNotifications',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="enableSmsNotifications"
                            className="text-base"
                          >
                            SMS Notifications
                          </Label>
                          <p className="text-sm text-gray-500">
                            Send notifications via SMS
                          </p>
                        </div>
                        <Switch
                          id="enableSmsNotifications"
                          checked={notificationSettings.enableSmsNotifications}
                          onCheckedChange={(checked) =>
                            handleNotificationSettingsChange(
                              'enableSmsNotifications',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="enablePushNotifications"
                            className="text-base"
                          >
                            Push Notifications
                          </Label>
                          <p className="text-sm text-gray-500">
                            Send browser push notifications
                          </p>
                        </div>
                        <Switch
                          id="enablePushNotifications"
                          checked={notificationSettings.enablePushNotifications}
                          onCheckedChange={(checked) =>
                            handleNotificationSettingsChange(
                              'enablePushNotifications',
                              checked
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-4">
                      Notification Events
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="adminEmailNotifications"
                            className="text-base"
                          >
                            Admin Notifications
                          </Label>
                          <p className="text-sm text-gray-500">
                            Send notifications to administrators
                          </p>
                        </div>
                        <Switch
                          id="adminEmailNotifications"
                          checked={notificationSettings.adminEmailNotifications}
                          onCheckedChange={(checked) =>
                            handleNotificationSettingsChange(
                              'adminEmailNotifications',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="userRegistrationNotifications"
                            className="text-base"
                          >
                            User Registration
                          </Label>
                          <p className="text-sm text-gray-500">
                            Send notifications when new users register
                          </p>
                        </div>
                        <Switch
                          id="userRegistrationNotifications"
                          checked={
                            notificationSettings.userRegistrationNotifications
                          }
                          onCheckedChange={(checked) =>
                            handleNotificationSettingsChange(
                              'userRegistrationNotifications',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="paymentNotifications"
                            className="text-base"
                          >
                            Payment Notifications
                          </Label>
                          <p className="text-sm text-gray-500">
                            Send notifications for payment events
                          </p>
                        </div>
                        <Switch
                          id="paymentNotifications"
                          checked={notificationSettings.paymentNotifications}
                          onCheckedChange={(checked) =>
                            handleNotificationSettingsChange(
                              'paymentNotifications',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="courseEnrollmentNotifications"
                            className="text-base"
                          >
                            Course Enrollment
                          </Label>
                          <p className="text-sm text-gray-500">
                            Send notifications for course enrollment events
                          </p>
                        </div>
                        <Switch
                          id="courseEnrollmentNotifications"
                          checked={
                            notificationSettings.courseEnrollmentNotifications
                          }
                          onCheckedChange={(checked) =>
                            handleNotificationSettingsChange(
                              'courseEnrollmentNotifications',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="systemAlertNotifications"
                            className="text-base"
                          >
                            System Alerts
                          </Label>
                          <p className="text-sm text-gray-500">
                            Send notifications for system events and alerts
                          </p>
                        </div>
                        <Switch
                          id="systemAlertNotifications"
                          checked={
                            notificationSettings.systemAlertNotifications
                          }
                          onCheckedChange={(checked) =>
                            handleNotificationSettingsChange(
                              'systemAlertNotifications',
                              checked
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-4">
                      SMS Gateway Configuration
                    </h3>
                    <div className="bg-gray-50 p-6 rounded-md text-center">
                      <Smartphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h4 className="text-lg font-medium mb-2">
                        SMS Gateway Setup
                      </h4>
                      <p className="text-gray-500 mb-4">
                        Configure SMS gateway settings to enable SMS
                        notifications.
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => {
                          toast({
                            title: 'Feature Coming Soon',
                            description:
                              'The SMS gateway configuration will be available in a future update.',
                          });
                        }}
                      >
                        Configure SMS Gateway
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  onClick={handleSaveSettings}
                  disabled={isSaving || !can(Permission.MANAGE_SYSTEM_SETTINGS)}
                  className="bg-gyaan-navy hover:bg-gyaan-navy/90"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Configure security and authentication settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">
                      Password Policy
                    </h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="passwordMinLength">
                            Minimum Password Length
                          </Label>
                          <Input
                            id="passwordMinLength"
                            type="number"
                            min="6"
                            max="32"
                            value={securitySettings.passwordMinLength}
                            onChange={(e) =>
                              handleSecuritySettingsChange(
                                'passwordMinLength',
                                e.target.value
                              )
                            }
                          />
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="passwordRequireUppercase"
                            className="text-base"
                          >
                            Require Uppercase Letters
                          </Label>
                          <p className="text-sm text-gray-500">
                            Passwords must contain at least one uppercase letter
                          </p>
                        </div>
                        <Switch
                          id="passwordRequireUppercase"
                          checked={securitySettings.passwordRequireUppercase}
                          onCheckedChange={(checked) =>
                            handleSecuritySettingsChange(
                              'passwordRequireUppercase',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="passwordRequireNumbers"
                            className="text-base"
                          >
                            Require Numbers
                          </Label>
                          <p className="text-sm text-gray-500">
                            Passwords must contain at least one number
                          </p>
                        </div>
                        <Switch
                          id="passwordRequireNumbers"
                          checked={securitySettings.passwordRequireNumbers}
                          onCheckedChange={(checked) =>
                            handleSecuritySettingsChange(
                              'passwordRequireNumbers',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="passwordRequireSymbols"
                            className="text-base"
                          >
                            Require Special Characters
                          </Label>
                          <p className="text-sm text-gray-500">
                            Passwords must contain at least one special
                            character
                          </p>
                        </div>
                        <Switch
                          id="passwordRequireSymbols"
                          checked={securitySettings.passwordRequireSymbols}
                          onCheckedChange={(checked) =>
                            handleSecuritySettingsChange(
                              'passwordRequireSymbols',
                              checked
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-4">
                      Account Security
                    </h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="accountLockoutAttempts">
                            Account Lockout Attempts
                          </Label>
                          <Input
                            id="accountLockoutAttempts"
                            type="number"
                            min="3"
                            max="10"
                            value={securitySettings.accountLockoutAttempts}
                            onChange={(e) =>
                              handleSecuritySettingsChange(
                                'accountLockoutAttempts',
                                e.target.value
                              )
                            }
                          />
                          <p className="text-xs text-gray-500">
                            Number of failed login attempts before account is
                            locked
                          </p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="accountLockoutDuration">
                            Account Lockout Duration (minutes)
                          </Label>
                          <Input
                            id="accountLockoutDuration"
                            type="number"
                            min="5"
                            max="1440"
                            value={securitySettings.accountLockoutDuration}
                            onChange={(e) =>
                              handleSecuritySettingsChange(
                                'accountLockoutDuration',
                                e.target.value
                              )
                            }
                          />
                          <p className="text-xs text-gray-500">
                            Duration in minutes for which account remains locked
                          </p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="sessionTimeout">
                          Session Timeout (minutes)
                        </Label>
                        <Input
                          id="sessionTimeout"
                          type="number"
                          min="5"
                          max="1440"
                          value={securitySettings.sessionTimeout}
                          onChange={(e) =>
                            handleSecuritySettingsChange(
                              'sessionTimeout',
                              e.target.value
                            )
                          }
                        />
                        <p className="text-xs text-gray-500">
                          Duration in minutes after which inactive sessions are
                          terminated
                        </p>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="enableTwoFactorAuth"
                            className="text-base"
                          >
                            Two-Factor Authentication
                          </Label>
                          <p className="text-sm text-gray-500">
                            Enable two-factor authentication for user accounts
                          </p>
                        </div>
                        <Switch
                          id="enableTwoFactorAuth"
                          checked={securitySettings.enableTwoFactorAuth}
                          onCheckedChange={(checked) =>
                            handleSecuritySettingsChange(
                              'enableTwoFactorAuth',
                              checked
                            )
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <Label
                            htmlFor="allowSocialLogin"
                            className="text-base"
                          >
                            Social Login
                          </Label>
                          <p className="text-sm text-gray-500">
                            Allow users to login using social media accounts
                          </p>
                        </div>
                        <Switch
                          id="allowSocialLogin"
                          checked={securitySettings.allowSocialLogin}
                          onCheckedChange={(checked) =>
                            handleSecuritySettingsChange(
                              'allowSocialLogin',
                              checked
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium mb-4">
                      Advanced Security
                    </h3>
                    <div className="bg-gray-50 p-6 rounded-md text-center">
                      <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h4 className="text-lg font-medium mb-2">
                        Advanced Security Settings
                      </h4>
                      <p className="text-gray-500 mb-4">
                        Configure advanced security settings such as IP
                        restrictions, CAPTCHA, and more.
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => {
                          toast({
                            title: 'Feature Coming Soon',
                            description:
                              'Advanced security settings will be available in a future update.',
                          });
                        }}
                      >
                        Configure Advanced Security
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  onClick={handleSaveSettings}
                  disabled={isSaving || !can(Permission.MANAGE_SYSTEM_SETTINGS)}
                  className="bg-gyaan-navy hover:bg-gyaan-navy/90"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
