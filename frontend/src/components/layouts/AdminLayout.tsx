import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Users,
  FileText,
  BookOpen,
  CreditCard,
  Bell,
  MessageSquare,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronDown,
  BarChart,
  BarChart2,
  Shield,
  Award,
  FileCheck,
  Upload,
  Building,
  ClipboardCheck,
  Mail,
  HelpCircle,
  List,
  Calendar,
} from 'lucide-react';
import RRUHeader from '@/components/RRUHeader';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { useAuth } from '@/providers/AuthProvider';
import { usePermissions } from '@/hooks/use-permissions';
import { Permission } from '@/utils/rbac';
import ErrorBoundary from '@/components/common/ErrorBoundary';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const location = useLocation();
  const { user, logout } = useAuth();
  const { can, role } = usePermissions();

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Define sidebar links with required permissions
  const sidebarLinks = [
    {
      name: 'Dashboard',
      path: '/admin/dashboard',
      icon: <BarChart size={20} />,
      permission: Permission.VIEW_DASHBOARD,
    },

    // Candidate Management Section
    {
      name: 'Candidates',
      path: '/admin/candidates',
      icon: <Users size={20} />,
      permission: Permission.VIEW_CANDIDATES,
    },
    {
      name: 'Applications',
      path: '/admin/applications',
      icon: <FileText size={20} />,
      permission: Permission.VIEW_CANDIDATES,
    },
    {
      name: 'Verification',
      path: '/admin/verification',
      icon: <FileCheck size={20} />,
      permission: Permission.VERIFY_CANDIDATES,
    },
    {
      name: 'Bulk Upload',
      path: '/admin/bulk-upload',
      icon: <Upload size={20} />,
      permission: Permission.BULK_UPLOAD_CANDIDATES,
    },

    // Course Management Section
    {
      name: 'Organizations',
      path: '/admin/organizations',
      icon: <Building size={20} />,
      permission: Permission.VIEW_ORGANIZATIONS,
    },
    {
      name: 'Courses',
      path: '/admin/courses',
      icon: <BookOpen size={20} />,
      permission: Permission.VIEW_COURSES,
    },
    {
      name: 'Subjects',
      path: '/admin/subjects',
      icon: <BookOpen size={20} />,
      permission: Permission.VIEW_SUBJECTS,
    },
    {
      name: 'Marks Management',
      path: '/admin/marks',
      icon: <ClipboardCheck size={20} />,
      permission: Permission.VIEW_MARKS,
    },
    {
      name: 'Marksheets',
      path: '/admin/marksheets',
      icon: <FileText size={20} />,
      permission: Permission.GENERATE_MARKSHEET,
    },

    // Communication Section
    {
      name: 'Notifications',
      path: '/admin/notifications',
      icon: <Bell size={20} />,
      permission: Permission.MANAGE_NOTIFICATIONS,
    },
    {
      name: 'Email/SMS',
      path: '/admin/communications',
      icon: <Mail size={20} />,
      permission: Permission.SEND_EMAILS,
    },

    // Support Section
    {
      name: 'Helpdesk',
      path: '/admin/helpdesk',
      icon: <HelpCircle size={20} />,
      permission: Permission.VIEW_CANDIDATES,
    },
    {
      name: 'Support Tickets',
      path: '/admin/support',
      icon: <MessageSquare size={20} />,
      permission: Permission.VIEW_CANDIDATES,
    },

    // Reports Section
    {
      name: 'Reports',
      path: '/admin/reports',
      icon: <FileText size={20} />,
      permission: Permission.VIEW_REPORTS,
    },
    {
      name: 'Analytics',
      path: '/admin/analytics',
      icon: <BarChart2 size={20} />,
      permission: Permission.VIEW_ANALYTICS,
    },

    // Administration Section
    {
      name: 'User Management',
      path: '/admin/users',
      icon: <Shield size={20} />,
      permission: Permission.VIEW_USERS,
    },
    {
      name: 'Audit Logs',
      path: '/admin/audit-logs',
      icon: <List size={20} />,
      permission: Permission.VIEW_AUDIT_LOGS,
    },
    {
      name: 'Settings',
      path: '/admin/settings',
      icon: <Settings size={20} />,
      permission: Permission.MANAGE_SYSTEM_SETTINGS,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* RRU Header */}
      <RRUHeader />

      {/* Navigation Header */}
      <header className="bg-white border-b sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className="mr-2 md:hidden"
            >
              {isSidebarOpen ? <X size={20} /> : <Menu size={20} />}
            </Button>
            <div className="flex items-center">
              <img
                src="/src/assets/images/RRULogo.png"
                alt="GyaanRaksha Logo"
                className="h-8 mr-2"
              />
              <span className="font-bold text-xl text-gyaan-navy hidden md:inline">
                GyaanRaksha Admin
              </span>
              <span className="font-bold text-xl text-gyaan-navy md:hidden">
                GRS Admin
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" className="relative">
              <Bell size={20} />
              <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-8 w-8 rounded-full"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/avatars/admin.png" alt="Admin" />
                    <AvatarFallback>AD</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  <div className="flex flex-col">
                    <span>{user?.fullName || 'Admin User'}</span>
                    <span className="text-xs text-gray-500">
                      {user?.email || '<EMAIL>'}
                    </span>
                    <span className="text-xs font-medium mt-1 bg-gray-100 px-2 py-0.5 rounded-full text-gray-700">
                      {role === 'super-admin'
                        ? 'Super Admin'
                        : role === 'verifier'
                        ? 'Verifier'
                        : role === 'course-coordinator'
                        ? 'Course Coordinator'
                        : 'Admin'}
                    </span>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User size={16} className="mr-2" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings size={16} className="mr-2" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={logout}>
                  <LogOut size={16} className="mr-2" />
                  <span>Logout</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="flex flex-1">
        {/* Sidebar */}
        <aside
          className={cn(
            'bg-white border-r w-64 shrink-0 fixed md:sticky top-[57px] h-[calc(100vh-57px)] overflow-y-auto transition-all z-10',
            isSidebarOpen ? 'left-0' : '-left-64'
          )}
        >
          <nav className="p-4">
            <div className="space-y-1">
              {/* Filter links based on user permissions */}
              {sidebarLinks
                .filter((link) => !link.permission || can(link.permission))
                .map((link) => (
                  <NavLink
                    key={link.path}
                    to={link.path}
                    className={({ isActive }) =>
                      cn(
                        'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
                        isActive
                          ? 'bg-gyaan-navy text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      )
                    }
                  >
                    <span className="mr-3">{link.icon}</span>
                    <span>{link.name}</span>
                  </NavLink>
                ))}
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main
          className={cn(
            'flex-1 p-6 transition-all',
            isSidebarOpen ? 'md:ml-0' : 'md:ml-0'
          )}
        >
          <div className="container mx-auto">
            <ErrorBoundary>{children}</ErrorBoundary>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;

// Helper component for TypeScript
const User = Users;
